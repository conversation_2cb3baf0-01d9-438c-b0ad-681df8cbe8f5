import React, { useRef, useEffect, useState } from 'react';
import { WebGLRenderer } from '../webgl/WebGLRenderer';
import { Camera } from '../webgl/Camera';
import { GeometryUtils } from '../webgl/Geometry';

const Canvas3D = ({ scene, onObjectSelect, selectedObject }) => {
  const canvasRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const animationFrameRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    try {
      // Initialize WebGL renderer
      rendererRef.current = new WebGLRenderer(canvasRef.current);
      
      // Initialize camera
      cameraRef.current = new Camera(canvasRef.current);
      
      // Create default room
      createDefaultRoom();
      
      setIsInitialized(true);
      
      // Start render loop
      startRenderLoop();
      
    } catch (error) {
      console.error('Failed to initialize WebGL:', error);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
      if (cameraRef.current) {
        cameraRef.current.dispose();
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !scene) return;
    
    // Update scene objects
    updateScene();
  }, [scene, isInitialized]);

  const createDefaultRoom = () => {
    const renderer = rendererRef.current;
    if (!renderer) return;

    // Create room geometry
    const roomGeometry = GeometryUtils.createRoom(10, 3, 10);
    const roomBuffers = {
      vertices: renderer.createBuffer(roomGeometry.vertices),
      normals: renderer.createBuffer(roomGeometry.normals),
      texCoords: renderer.createBuffer(roomGeometry.texCoords),
      indices: renderer.createBuffer(roomGeometry.indices, renderer.gl.ELEMENT_ARRAY_BUFFER),
      indexCount: roomGeometry.indexCount
    };

    // Add room to scene
    const room = {
      id: 'room',
      type: 'room',
      geometry: roomBuffers,
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scale: [1, 1, 1],
      material: {
        diffuse: [0.9, 0.9, 0.9],
        specular: [0.1, 0.1, 0.1],
        shininess: 4
      }
    };

    renderer.addObject(room);
  };

  const updateScene = () => {
    const renderer = rendererRef.current;
    if (!renderer || !scene) return;

    // Clear existing objects (except room)
    renderer.objects = renderer.objects.filter(obj => obj.id === 'room');

    // Add scene objects
    scene.objects?.forEach(sceneObject => {
      const geometry = createObjectGeometry(sceneObject);
      if (geometry) {
        const buffers = {
          vertices: renderer.createBuffer(geometry.vertices),
          normals: renderer.createBuffer(geometry.normals),
          texCoords: renderer.createBuffer(geometry.texCoords),
          indices: renderer.createBuffer(geometry.indices, renderer.gl.ELEMENT_ARRAY_BUFFER),
          indexCount: geometry.indexCount
        };

        const object = {
          id: sceneObject.id,
          type: sceneObject.type,
          geometry: buffers,
          position: sceneObject.position || [0, 0, 0],
          rotation: sceneObject.rotation || [0, 0, 0],
          scale: sceneObject.scale || [1, 1, 1],
          material: sceneObject.material || {
            diffuse: [0.7, 0.7, 0.7],
            specular: [0.3, 0.3, 0.3],
            shininess: 32
          }
        };

        renderer.addObject(object);
      }
    });
  };

  const createObjectGeometry = (sceneObject) => {
    switch (sceneObject.type) {
      case 'furniture':
        return GeometryUtils.createFurniture(sceneObject.furnitureType, sceneObject.dimensions);
      case 'wall':
        return GeometryUtils.createWall(sceneObject.width, sceneObject.height);
      case 'floor':
        return GeometryUtils.createPlane(sceneObject.width, sceneObject.depth);
      case 'cube':
        return GeometryUtils.createCube(sceneObject.size || 1);
      default:
        return GeometryUtils.createCube(1);
    }
  };

  const startRenderLoop = () => {
    const render = () => {
      if (rendererRef.current && cameraRef.current) {
        // Update camera in renderer
        rendererRef.current.setCamera(
          cameraRef.current.position,
          cameraRef.current.target,
          cameraRef.current.up
        );
        
        // Render scene
        rendererRef.current.render();
      }
      
      animationFrameRef.current = requestAnimationFrame(render);
    };
    
    render();
  };

  const handleCanvasClick = (event) => {
    if (!onObjectSelect) return;

    // Get mouse coordinates
    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Convert to normalized device coordinates
    const ndcX = (x / rect.width) * 2 - 1;
    const ndcY = -(y / rect.height) * 2 + 1;

    // Perform ray casting (simplified version)
    const clickedObject = performRayCasting(ndcX, ndcY);
    
    if (clickedObject) {
      onObjectSelect(clickedObject);
    }
  };

  const performRayCasting = (ndcX, ndcY) => {
    // Simplified ray casting - in a real implementation, you would:
    // 1. Convert NDC to world coordinates using inverse projection/view matrices
    // 2. Cast a ray from camera through the click point
    // 3. Test intersection with object bounding boxes/geometry
    
    // For now, return null (no object selected)
    return null;
  };

  const resetCamera = () => {
    if (cameraRef.current) {
      cameraRef.current.reset();
    }
  };

  const focusOnObject = (objectId) => {
    if (!cameraRef.current || !scene) return;

    const object = scene.objects?.find(obj => obj.id === objectId);
    if (object && object.position) {
      cameraRef.current.setTarget(object.position);
    }
  };

  return (
    <div className="canvas3d-container">
      <canvas
        ref={canvasRef}
        className="canvas3d"
        onClick={handleCanvasClick}
        style={{
          width: '100%',
          height: '100%',
          display: 'block',
          cursor: 'grab'
        }}
      />
      
      <div className="canvas3d-controls">
        <button onClick={resetCamera} className="control-button">
          Reset Camera
        </button>
        
        <div className="camera-info">
          <span>Left click: Rotate</span>
          <span>Right click: Pan</span>
          <span>Scroll: Zoom</span>
        </div>
      </div>
      
      {!isInitialized && (
        <div className="canvas3d-loading">
          <div>Initializing 3D Renderer...</div>
        </div>
      )}
    </div>
  );
};

export default Canvas3D;
