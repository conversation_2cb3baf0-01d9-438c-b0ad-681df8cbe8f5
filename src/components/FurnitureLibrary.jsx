import React, { useState } from 'react';
import { useDrag } from 'react-dnd';
import sceneStore from '../store/SceneStore';

const FurnitureItem = ({ type, name, icon, dimensions }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'furniture',
    item: { type, name, dimensions },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const handleClick = () => {
    // Add furniture to center of room
    sceneStore.addFurniture(type, [0, 0, 0]);
  };

  return (
    <div
      ref={drag}
      className={`furniture-item ${isDragging ? 'dragging' : ''}`}
      onClick={handleClick}
      title={`Click to add ${name} or drag to position`}
    >
      <div className="furniture-icon">{icon}</div>
      <div className="furniture-name">{name}</div>
      <div className="furniture-dimensions">
        {dimensions.width}×{dimensions.depth}×{dimensions.height}m
      </div>
    </div>
  );
};

const FurnitureCategory = ({ title, items, isExpanded, onToggle }) => {
  return (
    <div className="furniture-category">
      <div className="category-header" onClick={onToggle}>
        <span className="category-title">{title}</span>
        <span className={`category-arrow ${isExpanded ? 'expanded' : ''}`}>
          ▼
        </span>
      </div>
      {isExpanded && (
        <div className="category-items">
          {items.map((item) => (
            <FurnitureItem
              key={item.type}
              type={item.type}
              name={item.name}
              icon={item.icon}
              dimensions={item.dimensions}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const FurnitureLibrary = ({ isVisible, onClose }) => {
  const [expandedCategories, setExpandedCategories] = useState({
    seating: true,
    tables: true,
    bedroom: false,
    storage: false,
    lighting: false,
    decoration: false
  });

  const [searchTerm, setSearchTerm] = useState('');

  const furnitureCategories = {
    seating: {
      title: 'Seating',
      items: [
        {
          type: 'chair',
          name: 'Chair',
          icon: '🪑',
          dimensions: { width: 0.5, height: 0.8, depth: 0.5 }
        },
        {
          type: 'sofa',
          name: 'Sofa',
          icon: '🛋️',
          dimensions: { width: 2.0, height: 0.8, depth: 0.9 }
        },
        {
          type: 'armchair',
          name: 'Armchair',
          icon: '🪑',
          dimensions: { width: 0.8, height: 0.9, depth: 0.8 }
        },
        {
          type: 'stool',
          name: 'Stool',
          icon: '🪑',
          dimensions: { width: 0.4, height: 0.5, depth: 0.4 }
        }
      ]
    },
    tables: {
      title: 'Tables',
      items: [
        {
          type: 'table',
          name: 'Dining Table',
          icon: '🪑',
          dimensions: { width: 1.2, height: 0.75, depth: 0.8 }
        },
        {
          type: 'coffee_table',
          name: 'Coffee Table',
          icon: '🪑',
          dimensions: { width: 1.0, height: 0.4, depth: 0.6 }
        },
        {
          type: 'side_table',
          name: 'Side Table',
          icon: '🪑',
          dimensions: { width: 0.5, height: 0.6, depth: 0.5 }
        },
        {
          type: 'desk',
          name: 'Desk',
          icon: '🪑',
          dimensions: { width: 1.4, height: 0.75, depth: 0.7 }
        }
      ]
    },
    bedroom: {
      title: 'Bedroom',
      items: [
        {
          type: 'bed',
          name: 'Double Bed',
          icon: '🛏️',
          dimensions: { width: 2.0, height: 0.6, depth: 2.2 }
        },
        {
          type: 'single_bed',
          name: 'Single Bed',
          icon: '🛏️',
          dimensions: { width: 1.0, height: 0.6, depth: 2.0 }
        },
        {
          type: 'nightstand',
          name: 'Nightstand',
          icon: '🪑',
          dimensions: { width: 0.5, height: 0.6, depth: 0.4 }
        },
        {
          type: 'dresser',
          name: 'Dresser',
          icon: '🪑',
          dimensions: { width: 1.2, height: 0.8, depth: 0.5 }
        }
      ]
    },
    storage: {
      title: 'Storage',
      items: [
        {
          type: 'wardrobe',
          name: 'Wardrobe',
          icon: '🚪',
          dimensions: { width: 1.2, height: 2.0, depth: 0.6 }
        },
        {
          type: 'bookshelf',
          name: 'Bookshelf',
          icon: '📚',
          dimensions: { width: 0.8, height: 1.8, depth: 0.3 }
        },
        {
          type: 'cabinet',
          name: 'Cabinet',
          icon: '🗄️',
          dimensions: { width: 1.0, height: 0.9, depth: 0.4 }
        },
        {
          type: 'chest',
          name: 'Storage Chest',
          icon: '📦',
          dimensions: { width: 0.8, height: 0.5, depth: 0.5 }
        }
      ]
    },
    lighting: {
      title: 'Lighting',
      items: [
        {
          type: 'floor_lamp',
          name: 'Floor Lamp',
          icon: '💡',
          dimensions: { width: 0.3, height: 1.6, depth: 0.3 }
        },
        {
          type: 'table_lamp',
          name: 'Table Lamp',
          icon: '🕯️',
          dimensions: { width: 0.2, height: 0.5, depth: 0.2 }
        },
        {
          type: 'ceiling_light',
          name: 'Ceiling Light',
          icon: '💡',
          dimensions: { width: 0.5, height: 0.2, depth: 0.5 }
        }
      ]
    },
    decoration: {
      title: 'Decoration',
      items: [
        {
          type: 'plant',
          name: 'Plant',
          icon: '🪴',
          dimensions: { width: 0.3, height: 0.8, depth: 0.3 }
        },
        {
          type: 'vase',
          name: 'Vase',
          icon: '🏺',
          dimensions: { width: 0.2, height: 0.4, depth: 0.2 }
        },
        {
          type: 'picture',
          name: 'Picture Frame',
          icon: '🖼️',
          dimensions: { width: 0.5, height: 0.7, depth: 0.05 }
        },
        {
          type: 'mirror',
          name: 'Mirror',
          icon: '🪞',
          dimensions: { width: 0.6, height: 0.8, depth: 0.05 }
        }
      ]
    }
  };

  const toggleCategory = (categoryKey) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryKey]: !prev[categoryKey]
    }));
  };

  const filteredCategories = Object.entries(furnitureCategories).reduce((acc, [key, category]) => {
    if (searchTerm) {
      const filteredItems = category.items.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      if (filteredItems.length > 0) {
        acc[key] = { ...category, items: filteredItems };
      }
    } else {
      acc[key] = category;
    }
    return acc;
  }, {});

  const expandAll = () => {
    const allExpanded = Object.keys(furnitureCategories).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setExpandedCategories(allExpanded);
  };

  const collapseAll = () => {
    const allCollapsed = Object.keys(furnitureCategories).reduce((acc, key) => {
      acc[key] = false;
      return acc;
    }, {});
    setExpandedCategories(allCollapsed);
  };

  if (!isVisible) return null;

  return (
    <div className="furniture-library">
      <div className="library-header">
        <h3>Furniture Library</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="library-controls">
        <div className="search-container">
          <input
            type="text"
            placeholder="Search furniture..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="expand-controls">
          <button onClick={expandAll} className="control-btn">Expand All</button>
          <button onClick={collapseAll} className="control-btn">Collapse All</button>
        </div>
      </div>

      <div className="library-content">
        {Object.entries(filteredCategories).map(([key, category]) => (
          <FurnitureCategory
            key={key}
            title={category.title}
            items={category.items}
            isExpanded={expandedCategories[key]}
            onToggle={() => toggleCategory(key)}
          />
        ))}
        
        {Object.keys(filteredCategories).length === 0 && searchTerm && (
          <div className="no-results">
            No furniture found matching "{searchTerm}"
          </div>
        )}
      </div>

      <div className="library-footer">
        <div className="usage-hint">
          💡 Click to add to center, or drag to position
        </div>
      </div>
    </div>
  );
};

export default FurnitureLibrary;
