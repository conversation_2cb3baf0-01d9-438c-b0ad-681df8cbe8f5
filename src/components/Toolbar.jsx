import React, { useState, useEffect } from 'react';
import sceneStore from '../store/SceneStore';

const Toolbar = ({ 
  onToggleFurnitureLibrary, 
  onTogglePropertyPanel,
  onToggleFloorPlan,
  showFurnitureLibrary,
  showPropertyPanel,
  showFloorPlan 
}) => {
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [selectedTool, setSelectedTool] = useState('select');

  useEffect(() => {
    const updateHistoryState = () => {
      setCanUndo(sceneStore.canUndo());
      setCanRedo(sceneStore.canRedo());
    };

    const unsubscribe = sceneStore.subscribe(updateHistoryState);
    updateHistoryState();

    return unsubscribe;
  }, []);

  const handleUndo = () => {
    sceneStore.undo();
  };

  const handleRedo = () => {
    sceneStore.redo();
  };

  const handleClearScene = () => {
    if (window.confirm('Are you sure you want to clear the entire scene? This action cannot be undone.')) {
      sceneStore.clearScene();
    }
  };

  const handleExportScene = () => {
    const sceneData = sceneStore.exportScene();
    const blob = new Blob([sceneData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'interior-design.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImportScene = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const success = sceneStore.importScene(e.target.result);
          if (!success) {
            alert('Failed to import scene. Please check the file format.');
          }
        };
        reader.readAsText(file);
      }
    };
    
    input.click();
  };

  const handleDeleteSelected = () => {
    const selected = sceneStore.getSelectedObject();
    if (selected) {
      if (window.confirm(`Are you sure you want to delete the selected ${selected.type}?`)) {
        sceneStore.removeObject(selected.id);
      }
    }
  };

  const handleDuplicateSelected = () => {
    const selected = sceneStore.getSelectedObject();
    if (selected) {
      sceneStore.duplicateObject(selected.id);
    }
  };

  const toolButtons = [
    {
      id: 'select',
      icon: '👆',
      title: 'Select Tool',
      description: 'Select and move objects'
    },
    {
      id: 'rotate',
      icon: '🔄',
      title: 'Rotate Tool',
      description: 'Rotate selected objects'
    },
    {
      id: 'scale',
      icon: '📏',
      title: 'Scale Tool',
      description: 'Scale selected objects'
    },
    {
      id: 'measure',
      icon: '📐',
      title: 'Measure Tool',
      description: 'Measure distances and areas'
    }
  ];

  return (
    <div className="toolbar">
      <div className="toolbar-section">
        <h4>File</h4>
        <div className="toolbar-group">
          <button 
            className="toolbar-button"
            onClick={handleImportScene}
            title="Import Scene"
          >
            📁 Import
          </button>
          <button 
            className="toolbar-button"
            onClick={handleExportScene}
            title="Export Scene"
          >
            💾 Export
          </button>
          <button 
            className="toolbar-button warning"
            onClick={handleClearScene}
            title="Clear Scene"
          >
            🗑️ Clear
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <h4>Edit</h4>
        <div className="toolbar-group">
          <button 
            className={`toolbar-button ${!canUndo ? 'disabled' : ''}`}
            onClick={handleUndo}
            disabled={!canUndo}
            title="Undo"
          >
            ↶ Undo
          </button>
          <button 
            className={`toolbar-button ${!canRedo ? 'disabled' : ''}`}
            onClick={handleRedo}
            disabled={!canRedo}
            title="Redo"
          >
            ↷ Redo
          </button>
          <button 
            className="toolbar-button"
            onClick={handleDuplicateSelected}
            title="Duplicate Selected"
          >
            📋 Duplicate
          </button>
          <button 
            className="toolbar-button warning"
            onClick={handleDeleteSelected}
            title="Delete Selected"
          >
            🗑️ Delete
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <h4>Tools</h4>
        <div className="toolbar-group">
          {toolButtons.map(tool => (
            <button
              key={tool.id}
              className={`toolbar-button tool-button ${selectedTool === tool.id ? 'active' : ''}`}
              onClick={() => setSelectedTool(tool.id)}
              title={tool.description}
            >
              {tool.icon} {tool.title}
            </button>
          ))}
        </div>
      </div>

      <div className="toolbar-section">
        <h4>Panels</h4>
        <div className="toolbar-group">
          <button 
            className={`toolbar-button ${showFurnitureLibrary ? 'active' : ''}`}
            onClick={onToggleFurnitureLibrary}
            title="Toggle Furniture Library"
          >
            🪑 Furniture
          </button>
          <button 
            className={`toolbar-button ${showPropertyPanel ? 'active' : ''}`}
            onClick={onTogglePropertyPanel}
            title="Toggle Property Panel"
          >
            ⚙️ Properties
          </button>
          <button 
            className={`toolbar-button ${showFloorPlan ? 'active' : ''}`}
            onClick={onToggleFloorPlan}
            title="Toggle Floor Plan"
          >
            📐 Floor Plan
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <h4>View</h4>
        <div className="toolbar-group">
          <button 
            className="toolbar-button"
            onClick={() => {/* Reset camera view */}}
            title="Reset Camera"
          >
            🎥 Reset View
          </button>
          <button 
            className="toolbar-button"
            onClick={() => {/* Toggle wireframe */}}
            title="Toggle Wireframe"
          >
            🔲 Wireframe
          </button>
          <button 
            className="toolbar-button"
            onClick={() => {/* Toggle grid */}}
            title="Toggle Grid"
          >
            ⊞ Grid
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <h4>Quick Add</h4>
        <div className="toolbar-group">
          <button 
            className="toolbar-button"
            onClick={() => sceneStore.addFurniture('chair')}
            title="Add Chair"
          >
            🪑
          </button>
          <button 
            className="toolbar-button"
            onClick={() => sceneStore.addFurniture('table')}
            title="Add Table"
          >
            🪑
          </button>
          <button 
            className="toolbar-button"
            onClick={() => sceneStore.addFurniture('sofa')}
            title="Add Sofa"
          >
            🛋️
          </button>
          <button 
            className="toolbar-button"
            onClick={() => sceneStore.addFurniture('bed')}
            title="Add Bed"
          >
            🛏️
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <h4>Help</h4>
        <div className="toolbar-group">
          <button 
            className="toolbar-button"
            onClick={() => alert('Controls:\n• Left click + drag: Rotate camera\n• Right click + drag: Pan camera\n• Scroll wheel: Zoom\n• Click objects to select\n• Use toolbar to add furniture')}
            title="Show Controls Help"
          >
            ❓ Controls
          </button>
          <button 
            className="toolbar-button"
            onClick={() => window.open('https://github.com/your-repo/interior-designer-3d', '_blank')}
            title="View Documentation"
          >
            📖 Docs
          </button>
        </div>
      </div>
    </div>
  );
};

export default Toolbar;
