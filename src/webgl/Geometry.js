export class GeometryUtils {
  static createCube(size = 1) {
    const s = size / 2;
    
    const vertices = new Float32Array([
      // Front face
      -s, -s,  s,   s, -s,  s,   s,  s,  s,  -s,  s,  s,
      // Back face
      -s, -s, -s,  -s,  s, -s,   s,  s, -s,   s, -s, -s,
      // Top face
      -s,  s, -s,  -s,  s,  s,   s,  s,  s,   s,  s, -s,
      // Bottom face
      -s, -s, -s,   s, -s, -s,   s, -s,  s,  -s, -s,  s,
      // Right face
       s, -s, -s,   s,  s, -s,   s,  s,  s,   s, -s,  s,
      // Left face
      -s, -s, -s,  -s, -s,  s,  -s,  s,  s,  -s,  s, -s
    ]);
    
    const normals = new Float32Array([
      // Front face
       0,  0,  1,   0,  0,  1,   0,  0,  1,   0,  0,  1,
      // Back face
       0,  0, -1,   0,  0, -1,   0,  0, -1,   0,  0, -1,
      // Top face
       0,  1,  0,   0,  1,  0,   0,  1,  0,   0,  1,  0,
      // Bottom face
       0, -1,  0,   0, -1,  0,   0, -1,  0,   0, -1,  0,
      // Right face
       1,  0,  0,   1,  0,  0,   1,  0,  0,   1,  0,  0,
      // Left face
      -1,  0,  0,  -1,  0,  0,  -1,  0,  0,  -1,  0,  0
    ]);
    
    const texCoords = new Float32Array([
      // Front face
      0, 0,  1, 0,  1, 1,  0, 1,
      // Back face
      1, 0,  1, 1,  0, 1,  0, 0,
      // Top face
      0, 1,  0, 0,  1, 0,  1, 1,
      // Bottom face
      1, 1,  0, 1,  0, 0,  1, 0,
      // Right face
      1, 0,  1, 1,  0, 1,  0, 0,
      // Left face
      0, 0,  1, 0,  1, 1,  0, 1
    ]);
    
    const indices = new Uint16Array([
       0,  1,  2,   0,  2,  3,    // front
       4,  5,  6,   4,  6,  7,    // back
       8,  9, 10,   8, 10, 11,    // top
      12, 13, 14,  12, 14, 15,    // bottom
      16, 17, 18,  16, 18, 19,    // right
      20, 21, 22,  20, 22, 23     // left
    ]);
    
    return { vertices, normals, texCoords, indices, indexCount: indices.length };
  }

  static createPlane(width = 1, height = 1, widthSegments = 1, heightSegments = 1) {
    const vertices = [];
    const normals = [];
    const texCoords = [];
    const indices = [];
    
    const halfWidth = width / 2;
    const halfHeight = height / 2;
    
    for (let iy = 0; iy <= heightSegments; iy++) {
      const y = iy / heightSegments;
      for (let ix = 0; ix <= widthSegments; ix++) {
        const x = ix / widthSegments;
        
        vertices.push(
          (x - 0.5) * width,
          0,
          (y - 0.5) * height
        );
        
        normals.push(0, 1, 0);
        texCoords.push(x, y);
      }
    }
    
    for (let iy = 0; iy < heightSegments; iy++) {
      for (let ix = 0; ix < widthSegments; ix++) {
        const a = ix + (widthSegments + 1) * iy;
        const b = ix + (widthSegments + 1) * (iy + 1);
        const c = (ix + 1) + (widthSegments + 1) * (iy + 1);
        const d = (ix + 1) + (widthSegments + 1) * iy;
        
        indices.push(a, b, d);
        indices.push(b, c, d);
      }
    }
    
    return {
      vertices: new Float32Array(vertices),
      normals: new Float32Array(normals),
      texCoords: new Float32Array(texCoords),
      indices: new Uint16Array(indices),
      indexCount: indices.length
    };
  }

  static createWall(width = 1, height = 1) {
    const vertices = new Float32Array([
      -width/2, 0, 0,
       width/2, 0, 0,
       width/2, height, 0,
      -width/2, height, 0
    ]);
    
    const normals = new Float32Array([
      0, 0, 1,
      0, 0, 1,
      0, 0, 1,
      0, 0, 1
    ]);
    
    const texCoords = new Float32Array([
      0, 0,
      1, 0,
      1, 1,
      0, 1
    ]);
    
    const indices = new Uint16Array([
      0, 1, 2,
      0, 2, 3
    ]);
    
    return { vertices, normals, texCoords, indices, indexCount: indices.length };
  }

  static createRoom(width = 10, height = 3, depth = 10) {
    const vertices = [];
    const normals = [];
    const texCoords = [];
    const indices = [];
    let indexOffset = 0;
    
    // Floor
    const floor = this.createPlane(width, depth);
    vertices.push(...floor.vertices);
    normals.push(...floor.normals);
    texCoords.push(...floor.texCoords);
    indices.push(...floor.indices.map(i => i + indexOffset));
    indexOffset += floor.vertices.length / 3;
    
    // Ceiling
    const ceiling = this.createPlane(width, depth);
    for (let i = 0; i < ceiling.vertices.length; i += 3) {
      vertices.push(ceiling.vertices[i], height, ceiling.vertices[i + 2]);
      normals.push(0, -1, 0);
    }
    texCoords.push(...ceiling.texCoords);
    // Reverse winding for ceiling
    for (let i = 0; i < ceiling.indices.length; i += 3) {
      indices.push(
        ceiling.indices[i + 2] + indexOffset,
        ceiling.indices[i + 1] + indexOffset,
        ceiling.indices[i] + indexOffset
      );
    }
    indexOffset += ceiling.vertices.length / 3;
    
    // Walls
    const walls = [
      // Front wall
      { pos: [0, height/2, depth/2], rot: [0, 0, 0] },
      // Back wall
      { pos: [0, height/2, -depth/2], rot: [0, Math.PI, 0] },
      // Left wall
      { pos: [-width/2, height/2, 0], rot: [0, Math.PI/2, 0] },
      // Right wall
      { pos: [width/2, height/2, 0], rot: [0, -Math.PI/2, 0] }
    ];
    
    walls.forEach(wall => {
      const wallGeom = this.createWall(width, height);
      
      // Transform vertices based on wall position and rotation
      for (let i = 0; i < wallGeom.vertices.length; i += 3) {
        let x = wallGeom.vertices[i];
        let y = wallGeom.vertices[i + 1];
        let z = wallGeom.vertices[i + 2];
        
        // Apply rotation
        if (wall.rot[1] !== 0) {
          const cos = Math.cos(wall.rot[1]);
          const sin = Math.sin(wall.rot[1]);
          const newX = x * cos - z * sin;
          const newZ = x * sin + z * cos;
          x = newX;
          z = newZ;
        }
        
        // Apply translation
        vertices.push(x + wall.pos[0], y + wall.pos[1], z + wall.pos[2]);
      }
      
      // Transform normals
      for (let i = 0; i < wallGeom.normals.length; i += 3) {
        let nx = wallGeom.normals[i];
        let ny = wallGeom.normals[i + 1];
        let nz = wallGeom.normals[i + 2];
        
        // Apply rotation
        if (wall.rot[1] !== 0) {
          const cos = Math.cos(wall.rot[1]);
          const sin = Math.sin(wall.rot[1]);
          const newNx = nx * cos - nz * sin;
          const newNz = nx * sin + nz * cos;
          nx = newNx;
          nz = newNz;
        }
        
        normals.push(nx, ny, nz);
      }
      
      texCoords.push(...wallGeom.texCoords);
      indices.push(...wallGeom.indices.map(i => i + indexOffset));
      indexOffset += wallGeom.vertices.length / 3;
    });
    
    return {
      vertices: new Float32Array(vertices),
      normals: new Float32Array(normals),
      texCoords: new Float32Array(texCoords),
      indices: new Uint16Array(indices),
      indexCount: indices.length
    };
  }

  static createFurniture(type, dimensions = {}) {
    switch (type) {
      case 'chair':
        return this.createChair(dimensions);
      case 'table':
        return this.createTable(dimensions);
      case 'sofa':
        return this.createSofa(dimensions);
      case 'bed':
        return this.createBed(dimensions);
      default:
        return this.createCube(1);
    }
  }

  static createChair(dimensions = {}) {
    const { width = 0.5, height = 0.8, depth = 0.5 } = dimensions;
    
    // Simple chair made of cubes
    const parts = [
      // Seat
      { size: [width, 0.05, depth], pos: [0, 0.4, 0] },
      // Backrest
      { size: [width, 0.4, 0.05], pos: [0, 0.6, -depth/2 + 0.025] },
      // Legs
      { size: [0.05, 0.4, 0.05], pos: [-width/2 + 0.025, 0.2, -depth/2 + 0.025] },
      { size: [0.05, 0.4, 0.05], pos: [width/2 - 0.025, 0.2, -depth/2 + 0.025] },
      { size: [0.05, 0.4, 0.05], pos: [-width/2 + 0.025, 0.2, depth/2 - 0.025] },
      { size: [0.05, 0.4, 0.05], pos: [width/2 - 0.025, 0.2, depth/2 - 0.025] }
    ];
    
    return this.combineParts(parts);
  }

  static createTable(dimensions = {}) {
    const { width = 1.2, height = 0.75, depth = 0.8 } = dimensions;
    
    const parts = [
      // Top
      { size: [width, 0.05, depth], pos: [0, height - 0.025, 0] },
      // Legs
      { size: [0.05, height - 0.05, 0.05], pos: [-width/2 + 0.025, (height - 0.05)/2, -depth/2 + 0.025] },
      { size: [0.05, height - 0.05, 0.05], pos: [width/2 - 0.025, (height - 0.05)/2, -depth/2 + 0.025] },
      { size: [0.05, height - 0.05, 0.05], pos: [-width/2 + 0.025, (height - 0.05)/2, depth/2 - 0.025] },
      { size: [0.05, height - 0.05, 0.05], pos: [width/2 - 0.025, (height - 0.05)/2, depth/2 - 0.025] }
    ];
    
    return this.combineParts(parts);
  }

  static createSofa(dimensions = {}) {
    const { width = 2, height = 0.8, depth = 0.9 } = dimensions;
    
    const parts = [
      // Base
      { size: [width, 0.4, depth], pos: [0, 0.2, 0] },
      // Backrest
      { size: [width, 0.6, 0.2], pos: [0, 0.5, -depth/2 + 0.1] },
      // Armrests
      { size: [0.2, 0.6, depth], pos: [-width/2 + 0.1, 0.5, 0] },
      { size: [0.2, 0.6, depth], pos: [width/2 - 0.1, 0.5, 0] }
    ];
    
    return this.combineParts(parts);
  }

  static createBed(dimensions = {}) {
    const { width = 2, height = 0.6, depth = 2.2 } = dimensions;
    
    const parts = [
      // Mattress
      { size: [width, 0.2, depth], pos: [0, 0.3, 0] },
      // Frame
      { size: [width + 0.1, 0.1, depth + 0.1], pos: [0, 0.15, 0] },
      // Headboard
      { size: [width + 0.1, 0.8, 0.1], pos: [0, 0.6, -depth/2 - 0.05] }
    ];
    
    return this.combineParts(parts);
  }

  static combineParts(parts) {
    const vertices = [];
    const normals = [];
    const texCoords = [];
    const indices = [];
    let indexOffset = 0;
    
    parts.forEach(part => {
      const cube = this.createCube(1);
      
      // Scale and translate vertices
      for (let i = 0; i < cube.vertices.length; i += 3) {
        vertices.push(
          cube.vertices[i] * part.size[0] + part.pos[0],
          cube.vertices[i + 1] * part.size[1] + part.pos[1],
          cube.vertices[i + 2] * part.size[2] + part.pos[2]
        );
      }
      
      normals.push(...cube.normals);
      texCoords.push(...cube.texCoords);
      indices.push(...cube.indices.map(i => i + indexOffset));
      indexOffset += cube.vertices.length / 3;
    });
    
    return {
      vertices: new Float32Array(vertices),
      normals: new Float32Array(normals),
      texCoords: new Float32Array(texCoords),
      indices: new Uint16Array(indices),
      indexCount: indices.length
    };
  }

  static calculateNormals(vertices, indices) {
    const normals = new Array(vertices.length).fill(0);
    
    for (let i = 0; i < indices.length; i += 3) {
      const i1 = indices[i] * 3;
      const i2 = indices[i + 1] * 3;
      const i3 = indices[i + 2] * 3;
      
      const v1 = [vertices[i1], vertices[i1 + 1], vertices[i1 + 2]];
      const v2 = [vertices[i2], vertices[i2 + 1], vertices[i2 + 2]];
      const v3 = [vertices[i3], vertices[i3 + 1], vertices[i3 + 2]];
      
      const edge1 = [v2[0] - v1[0], v2[1] - v1[1], v2[2] - v1[2]];
      const edge2 = [v3[0] - v1[0], v3[1] - v1[1], v3[2] - v1[2]];
      
      const normal = [
        edge1[1] * edge2[2] - edge1[2] * edge2[1],
        edge1[2] * edge2[0] - edge1[0] * edge2[2],
        edge1[0] * edge2[1] - edge1[1] * edge2[0]
      ];
      
      normals[i1] += normal[0];
      normals[i1 + 1] += normal[1];
      normals[i1 + 2] += normal[2];
      
      normals[i2] += normal[0];
      normals[i2 + 1] += normal[1];
      normals[i2 + 2] += normal[2];
      
      normals[i3] += normal[0];
      normals[i3 + 1] += normal[1];
      normals[i3 + 2] += normal[2];
    }
    
    // Normalize
    for (let i = 0; i < normals.length; i += 3) {
      const length = Math.sqrt(normals[i] * normals[i] + normals[i + 1] * normals[i + 1] + normals[i + 2] * normals[i + 2]);
      if (length > 0) {
        normals[i] /= length;
        normals[i + 1] /= length;
        normals[i + 2] /= length;
      }
    }
    
    return new Float32Array(normals);
  }
}
