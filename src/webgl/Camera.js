import { vec3, mat4 } from 'gl-matrix';

export class Camera {
  constructor(canvas) {
    this.canvas = canvas;
    
    // Camera properties
    this.position = vec3.fromValues(0, 5, 10);
    this.target = vec3.fromValues(0, 0, 0);
    this.up = vec3.fromValues(0, 1, 0);
    
    // Projection properties
    this.fov = 45;
    this.near = 0.1;
    this.far = 100;
    
    // Control state
    this.isMouseDown = false;
    this.lastMouseX = 0;
    this.lastMouseY = 0;
    this.mouseButton = 0;
    
    // Orbit controls
    this.distance = vec3.distance(this.position, this.target);
    this.azimuth = 0; // horizontal rotation
    this.elevation = Math.PI / 6; // vertical rotation
    
    // Pan and zoom settings
    this.panSpeed = 0.01;
    this.rotateSpeed = 0.01;
    this.zoomSpeed = 0.1;
    this.minDistance = 1;
    this.maxDistance = 50;
    this.minElevation = -Math.PI / 2 + 0.1;
    this.maxElevation = Math.PI / 2 - 0.1;
    
    this.setupEventListeners();
    this.updatePosition();
  }

  setupEventListeners() {
    // Mouse events
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));
    
    // Touch events for mobile
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));
    
    // Prevent context menu
    this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
  }

  onMouseDown(event) {
    this.isMouseDown = true;
    this.mouseButton = event.button;
    this.lastMouseX = event.clientX;
    this.lastMouseY = event.clientY;
    
    // Prevent default behavior
    event.preventDefault();
  }

  onMouseMove(event) {
    if (!this.isMouseDown) return;
    
    const deltaX = event.clientX - this.lastMouseX;
    const deltaY = event.clientY - this.lastMouseY;
    
    if (this.mouseButton === 0) {
      // Left mouse button - rotate
      this.rotate(deltaX, deltaY);
    } else if (this.mouseButton === 1) {
      // Middle mouse button - pan
      this.pan(deltaX, deltaY);
    } else if (this.mouseButton === 2) {
      // Right mouse button - zoom
      this.zoom(deltaY);
    }
    
    this.lastMouseX = event.clientX;
    this.lastMouseY = event.clientY;
    
    event.preventDefault();
  }

  onMouseUp(event) {
    this.isMouseDown = false;
    event.preventDefault();
  }

  onWheel(event) {
    const delta = event.deltaY;
    this.zoom(delta * 0.1);
    event.preventDefault();
  }

  onTouchStart(event) {
    if (event.touches.length === 1) {
      // Single touch - rotate
      this.isMouseDown = true;
      this.mouseButton = 0;
      this.lastMouseX = event.touches[0].clientX;
      this.lastMouseY = event.touches[0].clientY;
    } else if (event.touches.length === 2) {
      // Two finger touch - zoom/pan
      this.isMouseDown = true;
      this.mouseButton = 1;
      
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      
      this.lastMouseX = (touch1.clientX + touch2.clientX) / 2;
      this.lastMouseY = (touch1.clientY + touch2.clientY) / 2;
      this.lastTouchDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
    }
    
    event.preventDefault();
  }

  onTouchMove(event) {
    if (!this.isMouseDown) return;
    
    if (event.touches.length === 1) {
      // Single touch - rotate
      const deltaX = event.touches[0].clientX - this.lastMouseX;
      const deltaY = event.touches[0].clientY - this.lastMouseY;
      
      this.rotate(deltaX, deltaY);
      
      this.lastMouseX = event.touches[0].clientX;
      this.lastMouseY = event.touches[0].clientY;
    } else if (event.touches.length === 2) {
      // Two finger touch - zoom/pan
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      
      const centerX = (touch1.clientX + touch2.clientX) / 2;
      const centerY = (touch1.clientY + touch2.clientY) / 2;
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      // Pan
      const deltaX = centerX - this.lastMouseX;
      const deltaY = centerY - this.lastMouseY;
      this.pan(deltaX, deltaY);
      
      // Zoom
      const deltaDistance = distance - this.lastTouchDistance;
      this.zoom(-deltaDistance * 0.01);
      
      this.lastMouseX = centerX;
      this.lastMouseY = centerY;
      this.lastTouchDistance = distance;
    }
    
    event.preventDefault();
  }

  onTouchEnd(event) {
    this.isMouseDown = false;
    event.preventDefault();
  }

  rotate(deltaX, deltaY) {
    this.azimuth -= deltaX * this.rotateSpeed;
    this.elevation += deltaY * this.rotateSpeed;
    
    // Clamp elevation
    this.elevation = Math.max(this.minElevation, Math.min(this.maxElevation, this.elevation));
    
    this.updatePosition();
  }

  pan(deltaX, deltaY) {
    // Get camera's right and up vectors
    const viewMatrix = mat4.create();
    mat4.lookAt(viewMatrix, this.position, this.target, this.up);
    
    const right = vec3.fromValues(viewMatrix[0], viewMatrix[4], viewMatrix[8]);
    const up = vec3.fromValues(viewMatrix[1], viewMatrix[5], viewMatrix[9]);
    
    // Scale pan speed by distance
    const panScale = this.distance * this.panSpeed;
    
    // Calculate pan offset
    const panOffset = vec3.create();
    vec3.scaleAndAdd(panOffset, panOffset, right, -deltaX * panScale);
    vec3.scaleAndAdd(panOffset, panOffset, up, deltaY * panScale);
    
    // Apply pan to target
    vec3.add(this.target, this.target, panOffset);
    
    this.updatePosition();
  }

  zoom(delta) {
    this.distance += delta * this.zoomSpeed;
    this.distance = Math.max(this.minDistance, Math.min(this.maxDistance, this.distance));
    
    this.updatePosition();
  }

  updatePosition() {
    // Convert spherical coordinates to cartesian
    const x = this.distance * Math.cos(this.elevation) * Math.cos(this.azimuth);
    const y = this.distance * Math.sin(this.elevation);
    const z = this.distance * Math.cos(this.elevation) * Math.sin(this.azimuth);
    
    // Set position relative to target
    vec3.set(this.position, 
      this.target[0] + x,
      this.target[1] + y,
      this.target[2] + z
    );
  }

  getViewMatrix() {
    const viewMatrix = mat4.create();
    mat4.lookAt(viewMatrix, this.position, this.target, this.up);
    return viewMatrix;
  }

  getProjectionMatrix(aspect) {
    const projectionMatrix = mat4.create();
    mat4.perspective(
      projectionMatrix,
      this.fov * Math.PI / 180,
      aspect,
      this.near,
      this.far
    );
    return projectionMatrix;
  }

  setTarget(target) {
    vec3.copy(this.target, target);
    this.updatePosition();
  }

  setPosition(position) {
    vec3.copy(this.position, position);
    
    // Update spherical coordinates
    const offset = vec3.create();
    vec3.subtract(offset, this.position, this.target);
    
    this.distance = vec3.length(offset);
    this.azimuth = Math.atan2(offset[2], offset[0]);
    this.elevation = Math.asin(offset[1] / this.distance);
  }

  lookAt(position, target, up) {
    vec3.copy(this.position, position);
    vec3.copy(this.target, target);
    vec3.copy(this.up, up);
    
    // Update spherical coordinates
    const offset = vec3.create();
    vec3.subtract(offset, this.position, this.target);
    
    this.distance = vec3.length(offset);
    this.azimuth = Math.atan2(offset[2], offset[0]);
    this.elevation = Math.asin(offset[1] / this.distance);
  }

  reset() {
    this.position = vec3.fromValues(0, 5, 10);
    this.target = vec3.fromValues(0, 0, 0);
    this.up = vec3.fromValues(0, 1, 0);
    this.distance = 10;
    this.azimuth = 0;
    this.elevation = Math.PI / 6;
    this.updatePosition();
  }

  dispose() {
    // Remove event listeners
    this.canvas.removeEventListener('mousedown', this.onMouseDown);
    this.canvas.removeEventListener('mousemove', this.onMouseMove);
    this.canvas.removeEventListener('mouseup', this.onMouseUp);
    this.canvas.removeEventListener('wheel', this.onWheel);
    this.canvas.removeEventListener('touchstart', this.onTouchStart);
    this.canvas.removeEventListener('touchmove', this.onTouchMove);
    this.canvas.removeEventListener('touchend', this.onTouchEnd);
    this.canvas.removeEventListener('contextmenu', (e) => e.preventDefault());
  }
}
