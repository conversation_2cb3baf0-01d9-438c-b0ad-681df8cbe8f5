import { mat4, vec3 } from 'gl-matrix';

export class WebGLRenderer {
  constructor(canvas) {
    this.canvas = canvas;
    this.gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    
    if (!this.gl) {
      throw new Error('WebGL not supported');
    }

    this.programs = new Map();
    this.buffers = new Map();
    this.textures = new Map();
    
    // Initialize WebGL settings
    this.initializeGL();
    
    // Create default shaders
    this.createDefaultShaders();
    
    // Scene data
    this.objects = [];
    this.lights = [];
    this.camera = {
      position: [0, 5, 10],
      target: [0, 0, 0],
      up: [0, 1, 0],
      fov: 45,
      near: 0.1,
      far: 100
    };
  }

  initializeGL() {
    const gl = this.gl;
    
    // Enable depth testing
    gl.enable(gl.DEPTH_TEST);
    gl.depthFunc(gl.LEQUAL);
    
    // Enable culling
    gl.enable(gl.CULL_FACE);
    gl.cullFace(gl.BACK);
    
    // Set clear color
    gl.clearColor(0.9, 0.9, 0.9, 1.0);
    
    // Set viewport
    this.resize();
  }

  createShader(type, source) {
    const gl = this.gl;
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      const error = gl.getShaderInfoLog(shader);
      gl.deleteShader(shader);
      throw new Error(`Shader compilation error: ${error}`);
    }
    
    return shader;
  }

  createProgram(vertexSource, fragmentSource) {
    const gl = this.gl;
    const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
    const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);
    
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      const error = gl.getProgramInfoLog(program);
      gl.deleteProgram(program);
      throw new Error(`Program linking error: ${error}`);
    }
    
    // Get attribute and uniform locations
    const numAttributes = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);
    const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    
    const attributes = {};
    const uniforms = {};
    
    for (let i = 0; i < numAttributes; i++) {
      const info = gl.getActiveAttrib(program, i);
      attributes[info.name] = gl.getAttribLocation(program, info.name);
    }
    
    for (let i = 0; i < numUniforms; i++) {
      const info = gl.getActiveUniform(program, i);
      uniforms[info.name] = gl.getUniformLocation(program, info.name);
    }
    
    return { program, attributes, uniforms };
  }

  createDefaultShaders() {
    // Basic vertex shader
    const vertexShaderSource = `
      attribute vec3 a_position;
      attribute vec3 a_normal;
      attribute vec2 a_texCoord;
      
      uniform mat4 u_modelMatrix;
      uniform mat4 u_viewMatrix;
      uniform mat4 u_projectionMatrix;
      uniform mat4 u_normalMatrix;
      
      varying vec3 v_normal;
      varying vec3 v_position;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 worldPosition = u_modelMatrix * vec4(a_position, 1.0);
        v_position = worldPosition.xyz;
        v_normal = normalize((u_normalMatrix * vec4(a_normal, 0.0)).xyz);
        v_texCoord = a_texCoord;
        
        gl_Position = u_projectionMatrix * u_viewMatrix * worldPosition;
      }
    `;
    
    // Basic fragment shader
    const fragmentShaderSource = `
      precision mediump float;
      
      varying vec3 v_normal;
      varying vec3 v_position;
      varying vec2 v_texCoord;
      
      uniform vec3 u_lightPosition;
      uniform vec3 u_lightColor;
      uniform vec3 u_ambientColor;
      uniform vec3 u_diffuseColor;
      uniform vec3 u_specularColor;
      uniform float u_shininess;
      uniform vec3 u_cameraPosition;
      
      void main() {
        vec3 normal = normalize(v_normal);
        vec3 lightDirection = normalize(u_lightPosition - v_position);
        vec3 viewDirection = normalize(u_cameraPosition - v_position);
        vec3 reflectDirection = reflect(-lightDirection, normal);
        
        // Ambient
        vec3 ambient = u_ambientColor * u_diffuseColor;
        
        // Diffuse
        float diff = max(dot(normal, lightDirection), 0.0);
        vec3 diffuse = diff * u_lightColor * u_diffuseColor;
        
        // Specular
        float spec = pow(max(dot(viewDirection, reflectDirection), 0.0), u_shininess);
        vec3 specular = spec * u_lightColor * u_specularColor;
        
        vec3 result = ambient + diffuse + specular;
        gl_FragColor = vec4(result, 1.0);
      }
    `;
    
    this.programs.set('basic', this.createProgram(vertexShaderSource, fragmentShaderSource));
  }

  createBuffer(data, type = this.gl.ARRAY_BUFFER) {
    const gl = this.gl;
    const buffer = gl.createBuffer();
    gl.bindBuffer(type, buffer);
    gl.bufferData(type, data, gl.STATIC_DRAW);
    return buffer;
  }

  resize() {
    const canvas = this.canvas;
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;
    
    if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
      canvas.width = displayWidth;
      canvas.height = displayHeight;
      this.gl.viewport(0, 0, displayWidth, displayHeight);
    }
  }

  render() {
    const gl = this.gl;
    
    this.resize();
    
    // Clear the canvas
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    
    // Calculate matrices
    const projectionMatrix = mat4.create();
    mat4.perspective(
      projectionMatrix,
      this.camera.fov * Math.PI / 180,
      this.canvas.width / this.canvas.height,
      this.camera.near,
      this.camera.far
    );
    
    const viewMatrix = mat4.create();
    mat4.lookAt(viewMatrix, this.camera.position, this.camera.target, this.camera.up);
    
    // Render objects
    this.objects.forEach(obj => this.renderObject(obj, viewMatrix, projectionMatrix));
  }

  renderObject(object, viewMatrix, projectionMatrix) {
    const gl = this.gl;
    const program = this.programs.get(object.shader || 'basic');
    
    if (!program) return;
    
    gl.useProgram(program.program);
    
    // Set matrices
    const modelMatrix = mat4.create();
    mat4.translate(modelMatrix, modelMatrix, object.position || [0, 0, 0]);
    mat4.rotateX(modelMatrix, modelMatrix, object.rotation?.[0] || 0);
    mat4.rotateY(modelMatrix, modelMatrix, object.rotation?.[1] || 0);
    mat4.rotateZ(modelMatrix, modelMatrix, object.rotation?.[2] || 0);
    mat4.scale(modelMatrix, modelMatrix, object.scale || [1, 1, 1]);
    
    const normalMatrix = mat4.create();
    mat4.invert(normalMatrix, modelMatrix);
    mat4.transpose(normalMatrix, normalMatrix);
    
    gl.uniformMatrix4fv(program.uniforms.u_modelMatrix, false, modelMatrix);
    gl.uniformMatrix4fv(program.uniforms.u_viewMatrix, false, viewMatrix);
    gl.uniformMatrix4fv(program.uniforms.u_projectionMatrix, false, projectionMatrix);
    gl.uniformMatrix4fv(program.uniforms.u_normalMatrix, false, normalMatrix);
    
    // Set lighting uniforms
    gl.uniform3fv(program.uniforms.u_lightPosition, [5, 5, 5]);
    gl.uniform3fv(program.uniforms.u_lightColor, [1, 1, 1]);
    gl.uniform3fv(program.uniforms.u_ambientColor, [0.2, 0.2, 0.2]);
    gl.uniform3fv(program.uniforms.u_cameraPosition, this.camera.position);
    
    // Set material uniforms
    gl.uniform3fv(program.uniforms.u_diffuseColor, object.material?.diffuse || [0.8, 0.8, 0.8]);
    gl.uniform3fv(program.uniforms.u_specularColor, object.material?.specular || [1, 1, 1]);
    gl.uniform1f(program.uniforms.u_shininess, object.material?.shininess || 32);
    
    // Bind geometry
    if (object.geometry) {
      this.bindGeometry(object.geometry, program);
      gl.drawElements(gl.TRIANGLES, object.geometry.indexCount, gl.UNSIGNED_SHORT, 0);
    }
  }

  bindGeometry(geometry, program) {
    const gl = this.gl;
    
    // Bind vertex buffer
    if (geometry.vertices && program.attributes.a_position !== undefined) {
      gl.bindBuffer(gl.ARRAY_BUFFER, geometry.vertices);
      gl.enableVertexAttribArray(program.attributes.a_position);
      gl.vertexAttribPointer(program.attributes.a_position, 3, gl.FLOAT, false, 0, 0);
    }
    
    // Bind normal buffer
    if (geometry.normals && program.attributes.a_normal !== undefined) {
      gl.bindBuffer(gl.ARRAY_BUFFER, geometry.normals);
      gl.enableVertexAttribArray(program.attributes.a_normal);
      gl.vertexAttribPointer(program.attributes.a_normal, 3, gl.FLOAT, false, 0, 0);
    }
    
    // Bind texture coordinate buffer
    if (geometry.texCoords && program.attributes.a_texCoord !== undefined) {
      gl.bindBuffer(gl.ARRAY_BUFFER, geometry.texCoords);
      gl.enableVertexAttribArray(program.attributes.a_texCoord);
      gl.vertexAttribPointer(program.attributes.a_texCoord, 2, gl.FLOAT, false, 0, 0);
    }
    
    // Bind index buffer
    if (geometry.indices) {
      gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, geometry.indices);
    }
  }

  addObject(object) {
    this.objects.push(object);
  }

  removeObject(object) {
    const index = this.objects.indexOf(object);
    if (index > -1) {
      this.objects.splice(index, 1);
    }
  }

  setCamera(position, target, up) {
    this.camera.position = position;
    this.camera.target = target;
    this.camera.up = up;
  }

  dispose() {
    // Clean up WebGL resources
    this.programs.forEach(program => {
      this.gl.deleteProgram(program.program);
    });
    
    this.buffers.forEach(buffer => {
      this.gl.deleteBuffer(buffer);
    });
    
    this.textures.forEach(texture => {
      this.gl.deleteTexture(texture);
    });
  }
}
