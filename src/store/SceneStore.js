import { v4 as uuidv4 } from 'uuid';

class SceneStore {
  constructor() {
    this.scene = {
      objects: [],
      room: {
        width: 10,
        height: 3,
        depth: 10
      },
      camera: {
        position: [0, 5, 10],
        target: [0, 0, 0]
      }
    };
    
    this.selectedObject = null;
    this.listeners = [];
    this.history = [];
    this.historyIndex = -1;
    this.maxHistorySize = 50;
    
    // Save initial state
    this.saveState();
  }

  // Event system
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  notify() {
    this.listeners.forEach(listener => listener(this.scene, this.selectedObject));
  }

  // Scene management
  getScene() {
    return { ...this.scene };
  }

  setScene(newScene) {
    this.scene = { ...newScene };
    this.notify();
  }

  // Object management
  addObject(objectData) {
    const object = {
      id: uuidv4(),
      type: objectData.type || 'cube',
      position: objectData.position || [0, 0, 0],
      rotation: objectData.rotation || [0, 0, 0],
      scale: objectData.scale || [1, 1, 1],
      material: objectData.material || {
        diffuse: [0.7, 0.7, 0.7],
        specular: [0.3, 0.3, 0.3],
        shininess: 32
      },
      ...objectData
    };

    this.scene.objects.push(object);
    this.saveState();
    this.notify();
    
    return object;
  }

  removeObject(objectId) {
    const index = this.scene.objects.findIndex(obj => obj.id === objectId);
    if (index > -1) {
      this.scene.objects.splice(index, 1);
      
      if (this.selectedObject?.id === objectId) {
        this.selectedObject = null;
      }
      
      this.saveState();
      this.notify();
    }
  }

  updateObject(objectId, updates) {
    const object = this.scene.objects.find(obj => obj.id === objectId);
    if (object) {
      Object.assign(object, updates);
      this.saveState();
      this.notify();
    }
  }

  getObject(objectId) {
    return this.scene.objects.find(obj => obj.id === objectId);
  }

  duplicateObject(objectId) {
    const original = this.getObject(objectId);
    if (original) {
      const duplicate = {
        ...original,
        id: uuidv4(),
        position: [
          original.position[0] + 1,
          original.position[1],
          original.position[2] + 1
        ]
      };
      
      this.scene.objects.push(duplicate);
      this.saveState();
      this.notify();
      
      return duplicate;
    }
  }

  // Selection management
  selectObject(objectId) {
    this.selectedObject = this.getObject(objectId);
    this.notify();
  }

  clearSelection() {
    this.selectedObject = null;
    this.notify();
  }

  getSelectedObject() {
    return this.selectedObject;
  }

  // Furniture library
  addFurniture(furnitureType, position = [0, 0, 0]) {
    const furnitureData = this.getFurnitureData(furnitureType);
    
    return this.addObject({
      type: 'furniture',
      furnitureType,
      position,
      ...furnitureData
    });
  }

  getFurnitureData(type) {
    const furnitureLibrary = {
      chair: {
        dimensions: { width: 0.5, height: 0.8, depth: 0.5 },
        material: {
          diffuse: [0.6, 0.4, 0.2],
          specular: [0.3, 0.3, 0.3],
          shininess: 16
        }
      },
      table: {
        dimensions: { width: 1.2, height: 0.75, depth: 0.8 },
        material: {
          diffuse: [0.5, 0.3, 0.1],
          specular: [0.4, 0.4, 0.4],
          shininess: 32
        }
      },
      sofa: {
        dimensions: { width: 2, height: 0.8, depth: 0.9 },
        material: {
          diffuse: [0.3, 0.3, 0.7],
          specular: [0.2, 0.2, 0.2],
          shininess: 8
        }
      },
      bed: {
        dimensions: { width: 2, height: 0.6, depth: 2.2 },
        material: {
          diffuse: [0.8, 0.8, 0.9],
          specular: [0.1, 0.1, 0.1],
          shininess: 4
        }
      }
    };

    return furnitureLibrary[type] || furnitureLibrary.chair;
  }

  // Room management
  updateRoom(roomData) {
    this.scene.room = { ...this.scene.room, ...roomData };
    this.saveState();
    this.notify();
  }

  // Transform operations
  moveObject(objectId, delta) {
    const object = this.getObject(objectId);
    if (object) {
      object.position[0] += delta[0];
      object.position[1] += delta[1];
      object.position[2] += delta[2];
      this.notify();
    }
  }

  rotateObject(objectId, delta) {
    const object = this.getObject(objectId);
    if (object) {
      object.rotation[0] += delta[0];
      object.rotation[1] += delta[1];
      object.rotation[2] += delta[2];
      this.notify();
    }
  }

  scaleObject(objectId, delta) {
    const object = this.getObject(objectId);
    if (object) {
      object.scale[0] *= delta[0];
      object.scale[1] *= delta[1];
      object.scale[2] *= delta[2];
      this.notify();
    }
  }

  // History management
  saveState() {
    // Remove any states after current index
    this.history = this.history.slice(0, this.historyIndex + 1);
    
    // Add new state
    this.history.push(JSON.stringify(this.scene));
    this.historyIndex++;
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.historyIndex--;
    }
  }

  undo() {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.scene = JSON.parse(this.history[this.historyIndex]);
      this.notify();
      return true;
    }
    return false;
  }

  redo() {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.scene = JSON.parse(this.history[this.historyIndex]);
      this.notify();
      return true;
    }
    return false;
  }

  canUndo() {
    return this.historyIndex > 0;
  }

  canRedo() {
    return this.historyIndex < this.history.length - 1;
  }

  // Import/Export
  exportScene() {
    return JSON.stringify(this.scene, null, 2);
  }

  importScene(sceneData) {
    try {
      const imported = JSON.parse(sceneData);
      this.scene = imported;
      this.selectedObject = null;
      this.saveState();
      this.notify();
      return true;
    } catch (error) {
      console.error('Failed to import scene:', error);
      return false;
    }
  }

  // Utility methods
  getObjectsInArea(center, radius) {
    return this.scene.objects.filter(obj => {
      const distance = Math.sqrt(
        Math.pow(obj.position[0] - center[0], 2) +
        Math.pow(obj.position[2] - center[2], 2)
      );
      return distance <= radius;
    });
  }

  checkCollision(objectId, newPosition) {
    const object = this.getObject(objectId);
    if (!object) return false;

    // Simple bounding box collision detection
    const objectBounds = this.getObjectBounds(object, newPosition);
    
    return this.scene.objects.some(other => {
      if (other.id === objectId) return false;
      
      const otherBounds = this.getObjectBounds(other);
      return this.boundsIntersect(objectBounds, otherBounds);
    });
  }

  getObjectBounds(object, position = null) {
    const pos = position || object.position;
    const scale = object.scale || [1, 1, 1];
    const dimensions = object.dimensions || { width: 1, height: 1, depth: 1 };
    
    const halfWidth = (dimensions.width * scale[0]) / 2;
    const halfHeight = (dimensions.height * scale[1]) / 2;
    const halfDepth = (dimensions.depth * scale[2]) / 2;
    
    return {
      min: [pos[0] - halfWidth, pos[1] - halfHeight, pos[2] - halfDepth],
      max: [pos[0] + halfWidth, pos[1] + halfHeight, pos[2] + halfDepth]
    };
  }

  boundsIntersect(bounds1, bounds2) {
    return (
      bounds1.min[0] <= bounds2.max[0] && bounds1.max[0] >= bounds2.min[0] &&
      bounds1.min[1] <= bounds2.max[1] && bounds1.max[1] >= bounds2.min[1] &&
      bounds1.min[2] <= bounds2.max[2] && bounds1.max[2] >= bounds2.min[2]
    );
  }

  clearScene() {
    this.scene.objects = [];
    this.selectedObject = null;
    this.saveState();
    this.notify();
  }
}

// Create singleton instance
const sceneStore = new SceneStore();

export default sceneStore;
