{"name": "redux", "version": "4.2.1", "description": "Predictable state container for JavaScript apps", "license": "MIT", "homepage": "http://redux.js.org", "repository": "github:reduxjs/redux", "bugs": "https://github.com/reduxjs/redux/issues", "keywords": ["redux", "reducer", "state", "predictable", "functional", "immutable", "hot", "live", "replay", "flux", "elm"], "authors": ["<PERSON> <<EMAIL>> (https://github.com/gaearon)", "<PERSON> <<EMAIL>> (https://github.com/acdlite)"], "main": "lib/redux.js", "unpkg": "dist/redux.js", "module": "es/redux.js", "typings": "./index.d.ts", "files": ["dist", "lib", "es", "src", "index.d.ts"], "scripts": {"clean": "rimraf lib dist es coverage", "format": "prettier --write \"{src,test}/**/*.{js,ts}\" index.d.ts \"**/*.md\"", "format:check": "prettier --list-different \"{src,test}/**/*.{js,ts}\" index.d.ts \"**/*.md\"", "lint": "eslint src test", "test": "jest", "test:watch": "npm test -- --watch", "test:cov": "npm test -- --coverage", "build": "rollup -c", "pretest": "npm run build", "prepublishOnly": "npm run clean && npm run format:check && npm run lint && npm test", "examples:lint": "eslint examples", "examples:test": "cross-env CI=true babel-node examples/testAll.js"}, "dependencies": {"@babel/runtime": "^7.9.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/node": "^7.8.7", "@babel/plugin-external-helpers": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.5", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/preset-flow": "^7.9.0", "@babel/preset-typescript": "^7.9.0", "@babel/register": "^7.9.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@types/jest": "^25.2.1", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-react-app": "^5.2.1", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^2.0.0", "glob": "^7.1.6", "jest": "^25.4.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.27.0", "rxjs": "^6.5.5", "typescript": "^3.8.3", "typings-tester": "^0.3.2"}, "npmName": "redux", "npmFileMap": [{"basePath": "/dist/", "files": ["*.js"]}], "jest": {"testRegex": "(/test/.*\\.spec\\.[tj]s)$", "coverageProvider": "v8"}, "sideEffects": false}